import { faArrowRight } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useEffect, useRef, useState } from "react";
import { Link, useLoaderData, useLocation, useNavigate, useRouteLoaderData } from "react-router-dom";
import AbunButton from "../../components/AbunButton/AbunButton";
import CalculatorLoadingScreen from "../../components/CalculatorLoadingScreen/CalculatorLoadingScreen";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import Icon from "../../components/Icon/Icon";
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
import { withAdminAndProductionCheck } from "../../utils/adminAndProductionCheck";
import {
  checkJobStatus,
  generateAICalculatorMutation,
  getAICalculatorDataQuery,
  modifyAICalculatorMutation,
  verifyToolsLoadingScriptMutation,
} from "../../utils/api";
import { BasePageData } from "../Base/Base";
import { PageData } from "../KeywordsResearchV2/KeywordResearch";
import { pageURL } from '../routes';
import './AICalculatorGenerator.min.css';
import AICalculatorTable from "./AICalculatorTable";
import { Player } from "@lottiefiles/react-lottie-player";

type AICalculatorVersion = {
  id: number;
  version_name: string;
  html_code: string;
  created_on: string;
};

// Browser Mockup Component
interface BrowserMockupProps {
  websiteDomain: string;
  calculatorTitle: string;
  children: React.ReactNode;
  isLoading?: boolean;
}

const BrowserMockup: React.FC<BrowserMockupProps> = ({ websiteDomain, calculatorTitle, children, isLoading = false }) => {
  // Create a URL-friendly slug from the calculator title
  const createSlug = (title: string) => {
    if (!title || title.trim() === '') {
      return 'calculator';
    }
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
      .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
      .trim() || 'calculator'; // Fallback if empty after processing
  };

  const slug = createSlug(calculatorTitle);
  const mockUrl = `https://${websiteDomain}/${slug}`;

  return (
    <div className="browser-mockup">
      <div className="browser-header">
        <div className="browser-controls">
          <div className="browser-control close"></div>
          <div className="browser-control minimize"></div>
          <div className="browser-control maximize"></div>
        </div>
        <div className="browser-address-bar">
          <span className="browser-url">{mockUrl}</span>
        </div>
      </div>
      <div className="browser-notification-bar">
        <div className="notification-content">
          <span className="notification-text">Your Existing Website NavBar</span>
        </div>
      </div>
      <div className="browser-content" style={{ position: 'relative' }}>
        {children}
        {isLoading && (
          <div className="browser-loading-overlay">
            <div className="browser-loading-content">
              <div className="browser-loading-spinner"></div>
              <p className="browser-loading-text">Updating calculator...</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

interface LocationState {
  calculatorId?: string;
  calculatorType?: string;
  userModifications?: string[];
  isGenerating?: boolean;
  jobId?: string;
  activeAIGeneratedTab?: string;
}


function AICalculatorGenerator() {
  // --------------------------- HOOKS ---------------------------
  const location = useLocation();
  const navigate = useNavigate();
  const state = location.state as LocationState;

  // --------------------------- STATES ---------------------------
  const [calculatorId, setCalculatorId] = useState<string | null>(state?.calculatorId || null);
  const [calculatorType, setCalculatorType] = useState<string>(state?.calculatorType || "");
  const [calculatorHTML, setCalculatorHTML] = useState<string>("");
  const [userInput, setUserInput] = useState<string>("");

  const [selectedVersion, setSelectedVersion] = useState(0);
  const [isChecked, setIsChecked] = useState(true);
  const [calculatorScriptTag, setCalculatorScriptTag] = useState("");
  const [calculatorDivTag, setCalculatorDivTag] = useState("");
  const [calcDescription, setCalcDescription] = useState<string>("");
  const [versions, setVersions] = useState<Array<AICalculatorVersion>>([]);
  const [jobId, setJobId] = useState<string>("");
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [showLoadingScreen, setShowLoadingScreen] = useState<boolean>(false);
  const [isModifying, setIsModifying] = useState<boolean>(false);
  const [activeAIGeneratedTab, setActiveAIGeneratedTab] = useState("ai-calculator");
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isVerified, setIsVerified] = useState(false);

  // Collapsible sections state
  const [expandedSections, setExpandedSections] = useState({
    update: true,      // Update Calculator is open by default
    versions: false,   // Versions is closed by default
    embed: false,      // Get Embed Code is closed by default
    verify: false      // Verify Script is closed by default
  });

  // --------------------------- REFS ---------------------------
  const errorAlertRef = useRef<any>(null);
  const successAlertRef = useRef<any>(null);
  const previewContainerRef = useRef<HTMLDivElement>(null);

  // --------------------------- PAGE DATA ---------------------------
  const pageData = useLoaderData() as PageData;
  const basePageData: BasePageData = useRouteLoaderData("base") as BasePageData;

  // --------------------------- UTILITY FUNCTIONS ---------------------------
  const getJobStorageKey = (calcId: string) => `calculator_job_${calcId}`;

  const saveJobToStorage = (calcId: string, jobId: string, type: 'generation' | 'modification') => {
    if (calcId) {
      localStorage.setItem(getJobStorageKey(calcId), JSON.stringify({ jobId, type, timestamp: Date.now() }));
    }
  };

  const getJobFromStorage = (calcId: string) => {
    if (!calcId) return null;
    const stored = localStorage.getItem(getJobStorageKey(calcId));
    return stored ? JSON.parse(stored) : null;
  };

  const clearJobFromStorage = (calcId: string) => {
    if (calcId) {
      localStorage.removeItem(getJobStorageKey(calcId));
    }
  };

  // --------------------------- QUERIES ---------------------------
  // Query for fetching calculator data
  const {
    isLoading: isLoadingCalculator,
    refetch: refetchCalculatorData
  } = useQuery({
    ...getAICalculatorDataQuery(calculatorId || ''),
    enabled: !!calculatorId,
    onSuccess: (response: any) => {
      if (response.data) {
        const { calculator_id, calc_type, code, encrypted_id, encrypted_tool_id, versions, ai_calculator_script_verified } = response.data.calculator_data;
        // Set calculator details
        setCalculatorId(calculator_id);
        setCalculatorType(calc_type);
        setCalculatorHTML(code);
        setCalculatorScriptTag(`<script async src=\"${process.env.REACT_APP_FRONTEND_DOMAIN}/js/${getSnippetFileName()}\" data-user-id=\"${encrypted_id}\" data-tool-name=\"ai-calculator\"></script>`);
        setCalculatorDivTag(`<div data-ai-calculator-id="${encrypted_tool_id}"></div>`);

        // Set verification status
        setIsVerified(ai_calculator_script_verified || false);

        // Set versions if available
        if (versions && versions.length > 0) {
          setVersions(versions);
          // Set the latest version as selected by default
          setSelectedVersion(versions[0].id);
        }
      }
    },
    onError: () => {
      errorAlertRef.current?.show("Failed to load calculator data");
    }
  });

  // Job status polling query
  const { data: jobStatusData } = useQuery({
    ...checkJobStatus(jobId),
    enabled: !!jobId && (isGenerating || isModifying),
    refetchInterval: 3000, // Poll every 3 seconds
    onSuccess: (response: any) => {
      if (response?.data?.status === "completed") {
        // Job completed successfully
        const wasModifying = isModifying;

        // Clear states
        setIsGenerating(false);
        setIsModifying(false);
        setShowLoadingScreen(false);
        setJobId("");

        // Clear from localStorage
        if (calculatorId) {
          clearJobFromStorage(calculatorId);
        }

        // Refetch calculator data to get the updated content
        if (calculatorId) {
          refetchCalculatorData().then(() => {
            const message = wasModifying
              ? "Calculator updated successfully!"
              : "Calculator generated successfully!";
            successAlertRef.current?.show(message);
            setTimeout(() => {
              successAlertRef.current?.close();
            }, 3000);
          });
        }
      } else if (response?.data?.status === "failed") {
        // Job failed
        const wasModifying = isModifying;

        setIsGenerating(false);
        setIsModifying(false);
        setShowLoadingScreen(false);
        setJobId("");

        // Clear from localStorage
        if (calculatorId) {
          clearJobFromStorage(calculatorId);
        }

        const errorMessage = wasModifying
          ? "Calculator modification failed. Please try again."
          : "Calculator generation failed. Please try again.";
        errorAlertRef.current?.show(errorMessage);
      }
    },
    onError: () => {
      setIsGenerating(false);
      setIsModifying(false);
      setShowLoadingScreen(false);
      setJobId("");

      // Clear from localStorage
      if (calculatorId) {
        clearJobFromStorage(calculatorId);
      }

      errorAlertRef.current?.show("Failed to check job status. Please try again.");
    }
  });

  // -------------------------- MUTATIONS ---------------------------
  // Mutation for generating a new calculator
  const generateAICalculatorMut = useMutation(generateAICalculatorMutation);

  // Mutation for modifying an existing calculator
  const modifyAICalculatorMut = useMutation(modifyAICalculatorMutation);

  // Mutation for verifying script
  const verifyScriptMutation = useMutation(verifyToolsLoadingScriptMutation);

  // --------------------------- EFFECTS ---------------------------
  // Update preview when HTML changes and execute JavaScript
  useEffect(() => {
    if (previewContainerRef.current && calculatorHTML) {
      // Create an iframe
      const iframe = document.createElement('iframe');
      iframe.style.width = '100%';
      iframe.style.height = '100%';
      iframe.style.border = 'none';

      // Set the HTML content of the iframe
      iframe.srcdoc = calculatorHTML;

      // Clear previous content and append the iframe
      previewContainerRef.current.innerHTML = "";
      previewContainerRef.current.appendChild(iframe);
    }
  }, [calculatorHTML]);

  // Handle initial state from navigation (when coming from generation request)
  useEffect(() => {
    if (state?.isGenerating && state?.jobId && !calculatorHTML) {
      setJobId(state.jobId);
      setIsGenerating(true);
      setShowLoadingScreen(true);
      setCalculatorId(state.calculatorId || null);
      setCalculatorType(state.calculatorType || "");
    }
  }, [state, calculatorHTML]);

  // Check for existing jobs in localStorage on component mount
  useEffect(() => {
    if (calculatorId && !jobId) {
      const storedJob = getJobFromStorage(calculatorId);
      if (storedJob && storedJob.jobId) {
        // Check if the job is not too old (24 hours max)
        const isJobTooOld = Date.now() - storedJob.timestamp > 24 * 60 * 60 * 1000;

        if (!isJobTooOld) {
          setJobId(storedJob.jobId);
          if (storedJob.type === 'generation') {
            setIsGenerating(true);
            setShowLoadingScreen(true);
          } else if (storedJob.type === 'modification') {
            setIsModifying(true);
          }
        } else {
          // Clear old job from storage
          clearJobFromStorage(calculatorId);
        }
      }
    }
  }, [calculatorId, jobId]);

  // Auto-hide loading screen if we have calculator data
  useEffect(() => {
    if (calculatorHTML && showLoadingScreen) {
      setShowLoadingScreen(false);
      setIsGenerating(false);
      setJobId("");
    }
  }, [calculatorHTML, showLoadingScreen]);

  useEffect(() => {
    if (state?.calculatorId && !calculatorHTML) {
      setCalculatorId(state.calculatorId);
      setCalculatorType(state.calculatorType || "");
    }
  }, [state, calculatorHTML]);

  useEffect(() => {
    if (location.state?.activeAIGeneratedTab) {
      setCalculatorId(null);
      setCalculatorHTML("");
    }
  }, [location.state]);

  // Safety timeout to prevent infinite loading (5 minutes max)
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    if (isGenerating && showLoadingScreen) {
      timeoutId = setTimeout(() => {
        setIsGenerating(false);
        setShowLoadingScreen(false);
        setJobId("");
        errorAlertRef.current?.show("Calculator generation is taking longer than expected. Please try again or check your calculators list.");
      }, 5 * 60 * 1000); // 5 minutes
    }

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [isGenerating, showLoadingScreen]);

  // --------------------------- HANDLERS ---------------------------
  const toggleSection = (section: 'update' | 'versions' | 'embed' | 'verify') => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const generateCalculator = () => {
    errorAlertRef.current?.close();
    successAlertRef.current?.close();

    if (!calculatorType || !calculatorType.trim()) {
      errorAlertRef.current?.show("Please enter a calculator type");
      return;
    }

    generateAICalculatorMut.mutate(
      { calc_type: calculatorType, calc_description: calcDescription },
      {
        onSuccess: (response) => {
          const data = response.data;

          if (data?.status === "submitted") {
            // New flow: Job submitted successfully
            setCalculatorId(data.calculator_id);
            setJobId(data.job_id);
            setIsGenerating(true);
            setShowLoadingScreen(true);

            // Save job to localStorage
            saveJobToStorage(data.calculator_id, data.job_id, 'generation');

            // Only navigate if we're not already on the calculator page
            // This prevents navigation loops
            if (!state?.calculatorId || state.calculatorId !== data.calculator_id) {
              navigate("/ai-calculator-generator", {
                state: {
                  calculatorId: data.calculator_id,
                  calculatorType: data.calc_type || calculatorType,
                  isGenerating: true,
                  jobId: data.job_id
                },
                replace: true // Use replace to avoid back button issues
              });
            }
          } else if (data?.status === "rejected") {
            // Handle limit reached or other rejections
            if (data.reason === "max_calculator_limit_reached") {
              errorAlertRef.current?.show(
                `You have reached your monthly calculator limit of ${data.limit}. Upgrade your plan for more calculators.`
              );
            } else {
              errorAlertRef.current?.show(data.message || "Request was rejected. Please try again.");
            }
          } else if (data?.status === "success") {
            // Legacy flow: Immediate success (backward compatibility)
            setCalculatorId(data.calculator_id);
            setCalculatorHTML(data.html_content);
            setCalculatorType(data.calc_type);
            successAlertRef.current?.show("Calculator generated successfully!");
            setTimeout(() => {
              successAlertRef.current?.close();
            }, 3000);
          }
        },
        onError: (error: any) => {
          // Handle 500 errors and other failures
          const errorMessage = error?.response?.data?.message || "Failed to generate calculator. Please try again.";
          errorAlertRef.current?.show(errorMessage);
        }
      }
    );
  };

  const requestModification = () => {
    errorAlertRef.current?.close();
    successAlertRef.current?.close();

    if ((!userInput || !userInput.trim()) || !calculatorId) {
      return;
    }

    const modificationText = userInput;
    setUserInput("");

    modifyAICalculatorMut.mutate(
      { calculator_id: calculatorId, modifications: modificationText },
      {
        onSuccess: (response) => {
          const data = response.data;

          if (data?.status === "submitted") {
            setJobId(data.job_id);
            setIsModifying(true);

            // Save job to localStorage
            if (calculatorId) {
              saveJobToStorage(calculatorId, data.job_id, 'modification');
            }

            successAlertRef.current?.show("Calculator modification submitted. Please wait while we update your calculator.");
            setTimeout(() => {
              successAlertRef.current?.close();
            }, 3000);
          } else {
            const errorMessage = response.data?.message || "Failed to modify calculator. Please try again.";
            errorAlertRef.current?.show(errorMessage);
            setTimeout(() => {
              errorAlertRef.current?.close();
            }, 3000);
          }
        },
        onError: (error: any) => {
          // Handle 500 errors and other failures
          const errorMessage = error?.response?.data?.message || "Failed to modify calculator. Please try again.";
          errorAlertRef.current?.show(errorMessage);
        }
      }
    );
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      successAlertRef.current?.show("Copied to clipboard!");
      setTimeout(() => {
        successAlertRef.current?.close();
      }, 3000);
    }).catch(() => {
      errorAlertRef.current?.show("Failed to copy to clipboard.");
    });
  };

  const backToList = () => {
    // navigate("/ai-calculator-generator");
    // navigate("/create-article");
    navigate("/ai-calculator-generator", {
      state: {
        activeAIGeneratedTab: "projects"
      }
    });
  };

  const getSnippetFileName = () => {
    if (process.env.REACT_APP_DRF_DOMAIN == 'https://api.abun.com') {
      return 'ai-calculator-snippet.js';
    } else if (process.env.REACT_APP_DRF_DOMAIN == 'https://staging.api.abun.com') {
      return 'ai-calculator-snippet-staging.js';
    } else {
      return 'ai-calculator-snippet-dev.js';
    }
  }

  const handleVersionChange = (versionId: number) => {
    setSelectedVersion(versionId);
    const selectedVersionData = versions.find(v => v.id === versionId);
    if (selectedVersionData) {
      setCalculatorHTML(selectedVersionData.html_code);
    }
  };

  const handleVerifyScript = () => {
    setIsVerifying(true);

    verifyScriptMutation.mutate({ toolName: "ai-calculator" }, {
      onSuccess: (response: any) => {
        setIsVerifying(false);
        const responseData = response.data;

        if (responseData.success) {
          setIsVerified(true);
          successAlertRef.current?.show("Script verification successful! Your website is properly configured.");
          setTimeout(() => {
            successAlertRef.current?.close();
          }, 5000);
        } else {
          // Handle different error types based on err_id
          let errorMessage = responseData.message || "Verification failed";

          switch (responseData.err_id) {
            case "NO_WEBSITE_FOUND":
              errorMessage = "No website found. Please ensure your website is properly connected.";
              break;
            case "WEBSITE_NOT_ACCESSIBLE":
              errorMessage = "Website not accessible. Please check if your website is online and accessible.";
              break;
            case "SCRIPT_TAG_NOT_FOUND":
              errorMessage = "Script tag not found on your website. Please ensure the script is properly installed.";
              break;
            case "INVALID_USER_EMAIL":
              errorMessage = "Invalid user email. Please contact support if this issue persists.";
              break;
            default:
              errorMessage = responseData.message || "Verification failed. Please try again.";
          }

          errorAlertRef.current?.show(errorMessage);
          setTimeout(() => {
            errorAlertRef.current?.close();
          }, 5000);
        }
      },
      onError: (error: any) => {
        setIsVerifying(false);
        console.error('Error verifying script:', error);

        const errorMessage = error?.response?.data?.message || "Failed to verify script. Please try again.";
        errorAlertRef.current?.show(errorMessage);
        setTimeout(() => {
          errorAlertRef.current?.close();
        }, 5000);
      }
    });
  };

  // Determine if any mutation is loading
  const isLoading = (state?.calculatorId && isLoadingCalculator) ||
    generateAICalculatorMut.isLoading ||
    modifyAICalculatorMut.isLoading ||
    isModifying;

  // --------------------------- RENDER ---------------------------
  // Show loading screen if calculator is being generated
  if (showLoadingScreen && isGenerating) {
    return <CalculatorLoadingScreen calculatorType={calculatorType} />;
  }

  return (
    <>
      <div className="ai-calculator-generator-container">
        <div className="ai-calculator-generator-content">
          {!calculatorHTML ? (
            <>
              <div className="mb-5 is-flex is-justify-content-space-between is-flex-wrap-wrap">
                <div className="ai-calculator-header">
                  <h2>AI Calculator Generator</h2>
                  <p className={"has-text-dark"}>
                    Create custom calculators for your website by simply describing what you need. <br />
                    Great for SEO or helping users solve real problems on your site. <br />
                    No code or formulas needed. Instantly embeddable on any site.
                  </p>
                </div>
                <span className="is-block mt-2">
                  {pageData.ai_calculator_remaining} AI Calculator Widgets remaining. <Link to={pageURL["subscriptionCredit"]} className="is-text has-text-black is-underlined" >View Credits</Link>
                </span>
              </div>

              <div className="tabs is-medium" style={{ scrollbarWidth: 'none' }}>
                <ul>
                  <li className={activeAIGeneratedTab === "ai-calculator" ? "is-active" : ""}>
                    <a onClick={() => setActiveAIGeneratedTab("ai-calculator")}>AI Calculator Generator</a>
                  </li>
                  <li className={activeAIGeneratedTab === "projects" ? "is-active" : ""}>
                    <a onClick={() => setActiveAIGeneratedTab("projects")}>Projects</a>
                  </li>
                </ul>
              </div>

              {activeAIGeneratedTab === "ai-calculator" &&
                <div className="is-flex is-align-items-center is-flex-direction-column ai-calculator-form-container">
                  <h3 >Describe a calculator idea, let AI build it for your site.</h3>
                  <hr />
                  <div className="ai-calculator-input-group">
                    <div className="field">
                      <label className="is-size-5 has-text-weight-medium has-text-black label">Calculator Name</label>
                      <div className="control">
                        <input
                          id="calculator-type"
                          className="input"
                          type="text"
                          placeholder="e.g., BMI, mortgage, tip, currency converter"
                          value={calculatorType}
                          onChange={(e) => setCalculatorType(e.target.value)}
                          disabled={isLoading}
                        />
                      </div>
                    </div>
                    <label className="mt-1 is-size-5 has-text-weight-medium has-text-black label">that does the following:</label>
                    <textarea
                      value={calcDescription}
                      onChange={(e) => setCalcDescription(e.target.value)}
                      className="textarea textarea-box"
                      placeholder="(Optional)"
                      disabled={isLoading}
                    ></textarea>
                    <button
                      className="mt-2 button is-responsive is-link ai-generate-calculator-button is-align-self-flex-start"
                      onClick={generateCalculator}
                      disabled={isLoading || (calculatorType.trim() === "")}
                    >
                      {isLoading ? "Generating..." :
                        <>
                          Generate <FontAwesomeIcon icon={faArrowRight} className="ml-2 is-size-6" />
                        </>
                      }
                    </button>
                  </div>
                </div >
              }

              {activeAIGeneratedTab === "projects" &&
                <AICalculatorTable />
              }

            </>
          ) : (
            <div
              className={`calculator-page-generator-container full-calculator-view`}
            >
              <div
                className={`card calculator-page-generator-header w-100 calculator-view`}
              >
                <div className="left-header-section">
                  <span
                    className="back-btn"
                    onClick={backToList}
                  >
                    <svg
                      className="back-btn"
                      width="30"
                      height="24"
                      viewBox="0 0 30 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M26.0435 12.0003H2.82031M2.82031 12.0003L12.8382 1.98242M2.82031 12.0003L12.8382 22.0181" stroke="black" strokeOpacity="0.5" strokeWidth="3" />
                    </svg>
                  </span>

                  <a href="/" onClick={(e) => { e.preventDefault(); navigate("/"); }}>
                    <svg className="abun-logo" width="52" height="48" viewBox="0 0 52 48" >
                      <rect x="2.125" y="4.41016" width="47.9091" height="42.0909" rx="6.5" fill="black" stroke="black" stroke-width="3" />
                      <rect x="0.5" y="0.5" width="49.9091" height="44.0909" rx="7.5" fill="white" stroke="black" />
                      <path d="M40 37.3373H29.7561V34.7968C28.2195 36.6746 24.8618 38 21.4472 38C17.3496 38 12 35.2939 12 29.2189C12 22.5917 17.3496 20.714 21.4472 20.714C25.0325 20.714 28.2764 21.8185 29.7561 23.641V20.8797C29.7561 19.002 27.9919 17.5661 24.6341 17.5661C22.0732 17.5661 19.1707 18.5602 17.0081 20.1617L13.5366 14.0316C17.2358 11.1598 22.3577 10 26.5122 10C33.3415 10 40 12.3195 40 21.211V37.3373ZM25.7154 31.5385C27.3089 31.5385 29.0732 31.0414 29.7561 30.1026V28.6114C29.0732 27.6726 27.3089 27.1755 25.7154 27.1755C24.0081 27.1755 22.1301 27.7278 22.1301 29.3846C22.1301 31.0414 24.0081 31.5385 25.7154 31.5385Z" fill="black" />
                    </svg>
                  </a>

                  <div className="Tabs">
                    <div className="Tab active">
                      Calculator Page
                    </div>
                  </div>
                </div>

                <div className="right-header-section">
                  <div className="editor-controls">

                    <svg
                      className={`collapse-button ${isSidebarCollapsed ? "" : "collapsed"}`}
                      onClick={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
                      width="20"
                      height="20"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      style={{ cursor: 'pointer', marginLeft: '8px' }}
                    >
                      <path fillRule="evenodd" clipRule="evenodd" d="M14 0H2C0.9 0 0 0.9 0 2V14C0 15.1 0.9 16 2 16H14C15.1 16 16 15.1 16 14V2C16 0.9 15.1 0 14 0ZM10 14.5H2C1.7 14.5 1.5 14.3 1.5 14V2C1.5 1.7 1.7 1.5 2 1.5H10V14.5ZM14.5 14C14.5 14.3 14.3 14.5 14 14.5H11.5V1.5H14C14.3 1.5 14.5 1.7 14.5 2V14Z" fill="#666" />
                    </svg>
                  </div>
                </div>
              </div>

              <div className={`calculator-page-generator-content calculator-view`}>
                <div className="calculator-result-section-new">
                  <div className="calculator-preview-main">
                    <div className="ai-preview-container">
                      {isLoading && !calculatorHTML ? (
                        <div className="loading-container" style={{
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          justifyContent: 'center',
                          height: '100%',
                          backgroundColor: '#f8f9fa',
                          borderRadius: '8px',
                          padding: '40px'
                        }}>
                          <Player
                            autoplay
                            loop
                            src="https://lottie.host/91a433df-05fa-4ab3-94b2-2c2a0a16a67f/2SoIqH8Kh3.json"
                            style={{ height: '300px', width: '300px' }}
                          />
                          <h1 style={{ color: '#666', marginBottom: '10px', fontWeight: 'bolder' }}>
                            An Amazing Calculator is being created for your site!
                          </h1>
                          <p style={{ color: '#888', textAlign: 'center', maxWidth: '300px' }}>
                            Creating your custom calculator for "{calculatorType}".
                            This may take a few moments.
                          </p>
                        </div>
                      ) : (
                        <div
                          style={{
                            border: '1px solid #d1d5db',
                            borderRadius: '8px',
                            backgroundColor: 'white',
                            width: '100%',
                            height: '88vh',
                            display: 'flex',
                            flexDirection: 'column',
                            position: 'relative',
                            overflow: 'hidden'
                          }}
                        >
                          <div
                            style={{
                              backgroundColor: '#f3f4f6',
                              padding: '8px 12px',
                              borderBottom: '1px solid #d1d5db',
                              display: 'flex',
                              alignItems: 'center',
                              gap: '8px',
                              minHeight: '40px',
                              position: 'sticky',
                              top: 0,
                              zIndex: 10,
                              flexShrink: 0
                            }}
                          >
                            <div style={{ display: 'flex', gap: '6px' }}>
                              <div
                                style={{
                                  width: '12px',
                                  height: '12px',
                                  borderRadius: '50%',
                                  backgroundColor: '#ff5f57'
                                }}
                              />
                              <div
                                style={{
                                  width: '12px',
                                  height: '12px',
                                  borderRadius: '50%',
                                  backgroundColor: '#ffbd2e'
                                }}
                              />
                              <div
                                style={{
                                  width: '12px',
                                  height: '12px',
                                  borderRadius: '50%',
                                  backgroundColor: '#28ca42'
                                }}
                              />
                            </div>

                            {/* URL Bar */}
                            <div
                              style={{
                                flex: 1,
                                backgroundColor: 'white',
                                border: '1px solid #d1d5db',
                                borderRadius: '4px',
                                padding: '6px 12px',
                                fontSize: '14px',
                                color: '#6b7280',
                                marginLeft: '8px'
                              }}
                            >
                              https://<span>{basePageData?.active_website_domain || 'example.com'}</span>/<span>{calculatorType.toLowerCase().replace(/\s+/g, '-')}</span>
                            </div>
                          </div>

                          {/* Navbar - Sticky */}
                          <div
                            style={{
                              backgroundColor: '#e5e7eb',
                              padding: '20px 16px',
                              borderBottom: '1px solid #d1d5db',
                              fontSize: '14px',
                              color: '#374151',
                              textAlign: 'center',
                              minHeight: '60px',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              position: 'sticky',
                              top: '40px',
                              zIndex: 9,
                              flexShrink: 0
                            }}
                          >
                            Your Existing Website Navbar
                          </div>

                          {/* Calculator Content */}
                          <div
                            className="ai-preview-content"
                            ref={previewContainerRef}
                            style={{
                              width: '100%',
                              height: '100%',
                              backgroundColor: 'white',
                              display: 'block',
                              flex: 1,
                              overflow: 'auto'
                            }}
                          />
                        </div>
                      )}
                    </div>
                  </div>

                  <div className={`calculator-sidebar ${isSidebarCollapsed ? 'collapsed' : ''}`}>
                    {/* Update Calculator Dropdown */}
                    <div className="sidebar-section">
                      <div
                        className={`sidebar-dropdown-header version-header ${expandedSections.update ? "active" : ""}`}
                        onClick={() => toggleSection('update')}
                      >
                        <span><h6>What changes do you want in the Calculator?</h6></span>
                      </div>

                      {expandedSections.update && (
                        <div className="sidebar-dropdown-content">
                          <textarea
                            className="sidebar-textarea"
                            placeholder="Add the following...."
                            value={userInput}
                            onChange={(e) => setUserInput(e.target.value)}
                            disabled={isLoading || isModifying}
                          />
                          <div className="field mt-3" style={{ marginTop: '10px' }}>
                            <label className="checkbox" style={{ fontSize: '14px', color: '#374151', display: 'flex', alignItems: 'center' }}>
                              <input
                                type="checkbox"
                                checked={isChecked}
                                onChange={() => setIsChecked(!isChecked)}
                                style={{ marginRight: '8px' }}
                              />
                              Make the Calculator Name as H1 Tag
                            </label>
                          </div>
                          <button
                            className="sidebar-button update-btn"
                            onClick={requestModification}
                            disabled={isModifying || !userInput.trim() || isLoading}
                            style={{
                              opacity: (isModifying || isLoading) ? 0.6 : 1,
                              cursor: (isModifying || isLoading) ? 'not-allowed' : 'pointer',
                              position: 'relative'
                            }}
                          >
                            {isModifying ? (
                              <span className="button-content">
                                <span className="spinner"></span>
                                Updating...
                              </span>
                            ) : (
                              "Update Calculator"
                            )}
                          </button>
                        </div>
                      )}
                    </div>

                    {/* Version History Dropdown */}
                    <div className="sidebar-section">
                      <div
                        className={`sidebar-dropdown-header version-header ${expandedSections.versions ? "active" : ""}`}
                        onClick={() => toggleSection('versions')}
                      >
                        <span><h6>Version History</h6></span>
                        <span className="version-count">
                          {versions.length}
                        </span>
                      </div>

                      {expandedSections.versions && (
                        <div className="sidebar-dropdown-content">
                          {versions.length === 0 ? (
                            <p className="empty-versions">
                              No versions available for this calculator.
                            </p>
                          ) : (
                            <div className="versions-list-sidebar">
                              {versions.map((version, index) => (
                                <div
                                  key={version.id}
                                  className={`version-item-sidebar ${selectedVersion === version.id ? 'current-version' : ''}`}
                                >
                                  <div className="version-header-sidebar">
                                    <div className="version-info-sidebar">
                                      <div className="version-number-sidebar">
                                        <span>
                                          {index === versions.length - 1
                                            ? "Original"
                                            : `v${versions.length - index}`
                                          }
                                        </span>
                                      </div>
                                    </div>
                                    <div className="version-actions-sidebar" style={{
                                      display: 'flex',
                                      justifyContent: 'center',
                                      alignItems: 'center',
                                      gap: '8px'
                                    }}>
                                      {selectedVersion === version.id ? (
                                        <button className="sidebar-button small switch-btn" style={{ backgroundColor: '#10b981' }}>
                                          Current
                                        </button>
                                      ) : (
                                        <button
                                          className="sidebar-button small switch-btn"
                                          onClick={() => handleVersionChange(version.id)}
                                        >
                                          Switch
                                        </button>
                                      )}
                                    </div>
                                  </div>
                                  <div className="version-description-sidebar">
                                    {version.version_name || 'No description available'}
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      )}
                    </div>

                    {/* Get Embed Code Dropdown */}
                    {!isLoading && !isModifying && calculatorHTML && (
                      <div className="sidebar-section">
                        <div
                          className={`sidebar-dropdown-header version-header ${expandedSections.embed ? "active" : ""}`}
                          onClick={() => {
                            toggleSection('embed');
                          }}
                        >
                          <span><h6>Get Embed Code</h6></span>
                        </div>

                        {expandedSections.embed && (
                          <div className="sidebar-dropdown-content">
                            <div>
                              <div style={{ marginBottom: '15px' }}>
                                <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>
                                  Add this Script Tag to your head:
                                </label>
                                <textarea
                                  className="sidebar-textarea embed-code"
                                  readOnly
                                  value={calculatorScriptTag}
                                  style={{ minHeight: '90px', fontSize: '12px' }}
                                />
                                <button
                                  className="sidebar-button copy-btn"
                                  onClick={() => copyToClipboard(calculatorScriptTag)}
                                  disabled={!calculatorScriptTag}
                                >
                                  Copy
                                </button>
                              </div>
                              <div style={{ marginBottom: '15px' }}>
                                <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>
                                  Add this Div Tag in the body where you want to load:
                                </label>
                                <textarea
                                  className="sidebar-textarea embed-code"
                                  readOnly
                                  value={calculatorDivTag}
                                  style={{ minHeight: '110px', fontSize: '12px' }}
                                />
                                <button
                                  className="sidebar-button copy-btn"
                                  onClick={() => copyToClipboard(calculatorDivTag)}
                                  disabled={!calculatorDivTag}
                                >
                                  Copy
                                </button>
                              </div>

                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Verify Script Section */}
                    {!isLoading && !isModifying && calculatorHTML && (
                      <div className="sidebar-section">
                        <div
                          className={`sidebar-dropdown-header version-header ${expandedSections.verify ? "active" : ""}`}
                          onClick={() => toggleSection('verify')}
                        >
                          <span><h6>Verify Script Installation</h6></span>
                        </div>

                        {expandedSections.verify && (
                          <div className="sidebar-dropdown-content">
                            <div>
                              <div style={{ marginBottom: '15px' }}>
                                <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>
                                  Verify that the script is properly installed on your website:
                                </label>
                                <AbunButton
                                  className="sidebar-button copy-btn"
                                  type="success"
                                  clickHandler={handleVerifyScript}
                                  disabled={isVerifying || isVerified}
                                >
                                  {isVerified ? 'Verified' : isVerifying ? 'Verifying...' : 'Verify'}
                                </AbunButton>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div >

        <ErrorAlert ref={errorAlertRef} />
        <SuccessAlert ref={successAlertRef} />
      </div >
    </>
  );
};

export default withAdminAndProductionCheck(AICalculatorGenerator);