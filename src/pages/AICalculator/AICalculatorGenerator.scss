@import "../../assets/themes/mainTheme";
@import "../../assets/fonts/customFonts";

@import "bulma/sass/utilities/all";
@import "bulma/sass/base/all";
@import "bulma/sass/helpers/typography";
@import "bulma/sass/grid/columns";
@import "bulma/sass/elements/container";
@import "bulma/sass/form/_all";
@import "bulma/sass/components/tabs";
@import "bulma/sass/helpers/all";
@import "bulma/sass/elements/all";

@import "../../assets/bulma-overrides/bulmaOverrides";

/* Main container for AI Calculator Generator */
.ai-calculator-generator-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: $primary-font !important;
  // max-width: 1500px;
  overflow: visible;
  width: 100%;
  padding: 0; // Remove any default padding
  margin: 0; // Remove any default margin

  /* Responsive adjustments */
  // @media (max-width: 480px) {
  //   height: auto;
  //   width: 100%;
  // }

  // @media (max-width: 769px) {
  //   height: auto;
  //   width: 100%;
  // }

  // @media (min-width: 1024px) {
  //   width: 100vw;
  //   max-width: 100%;
  // }

  /* Header section with back button */
  // .ai-calculator-generator-header {
  //   position: relative;
  //   width: 100%;
  //   height: 50px;

  //   .ai-calculator-back-button {
  //     background: none;
  //     border: none;
  //     cursor: pointer;
  //     display: flex;
  //     align-items: center;
  //     padding: 8px;
  //     border-radius: 4px;
  //     transition: background-color 0.2s;
  //     position: absolute;
  //     left: 0;

  //     &:hover {
  //       background-color: rgba(0, 0, 0, 0.05);
  //     }

  //     svg {
  //       width: 24px;
  //       height: 24px;
  //     }
  //   }
  // }

  /* Content section */
  .ai-calculator-generator-content {
    display: flex;
    flex-direction: column;
    width: 95%;
    padding: 0; // Remove any default padding
    margin: 1.2rem;

    .ai-calculator-header {

      h2 {
        font-family: $primary-font !important;
        font-size: 2rem !important;
        font-weight: 600 !important;
        margin-bottom: 4px;
      }

      p {
        // color: rgba(0, 0, 0, .698);
        font-family: $secondary-font !important;
        font-size: 1.125rem !important;
      }
    }

    /* Input card for calculator type */
    .ai-calculator-form-container {
      border: 1px solid #e7e7e7;
      border-radius: 8px;
      margin-bottom: 1.5rem;
      width: fit-content;

      @media (max-width: 768px) {
        width: 100%;
        margin-bottom: 1rem;
      }

      h3 {
        font-size: 1.4rem;
        font-weight: 600;
        padding: 1rem;
        align-self: start;

        @media (max-width: 768px) {
          font-size: 1.2rem;
          padding: 0.8rem;
        }

        @media (max-width: 480px) {
          font-size: 1.1rem;
          padding: 0.6rem;
        }
      }

      hr {
        background: #e7e7e7;
        height: 1px;
        margin: 0;
        width: 100%;
      }

      .ai-calculator-input-group {
        justify-items: center;
        display: flex;
        flex-direction: column;
        padding: 1rem;
        padding-left: 1.6rem;
        width: 100%;

        @media (max-width: 768px) {
          padding: 0.8rem;
          padding-left: 1rem;
        }

        @media (max-width: 480px) {
          padding: 0.6rem;
          padding-left: 0.8rem;
        }

        .field {
          width: 100%;

          .input {
            @media (max-width: 480px) {
              font-size: 14px;
            }
          }

          .label {
            @media (max-width: 480px) {
              font-size: 16px;
            }
          }
        }

        .textarea-box {
          height: 170px;
          min-height: 86px;
          margin-bottom: 16px;
          resize: vertical;
          width: 100%;
          max-width: 415px;
          border-radius: 10px;
          padding: 17px;
          font-family: $secondary-font;

          @media (max-width: 768px) {
            height: 140px;
            min-height: 70px;
            max-width: 100%;
            padding: 15px;
          }

          @media (max-width: 480px) {
            height: 120px;
            min-height: 60px;
            padding: 12px;
            font-size: 14px;
          }
        }

        .ai-generate-calculator-button {
          @media (max-width: 480px) {
            width: 100%;
            font-size: 14px;
            padding: 12px;
          }
        }
      }
    }
  }

  .horizontal-line {
    width: 100%;
    height: 1px;
    margin-bottom: 0;
    background-color: #dedbdb;
  }

  .calculator-page-generator-container {
  font-family: $primary-font !important;
  background-color: #ffffff;

  &.full-calculator-view {
    margin: 0;
    padding: 0;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh; 
    overflow: hidden; 
    z-index: 99;
    background-color: #fff;
  }

  // Header styles
  .calculator-page-generator-header {
    position: relative;
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &.calculator-view {
      position: sticky;
      top: 0;
      z-index: 99;
      background-color: #fff;
      border-bottom: none;
      box-shadow: none;
      height: 6rem;
      padding: 2rem 1.5rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .left-header-section {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      flex-wrap: nowrap; 
      margin-left: 0;
      flex: 1;
      min-width: 0; 

      .back-btn {
        cursor: pointer;
        margin-right: 1rem;
        flex-shrink: 0; 
        
        svg {
          width: 30px;
          height: 24px;
        }
      }

      .abun-logo {
        margin: 0 1.25rem;
        width: 52px;
        height: 48px;
        flex-shrink: 0; 
      }

      .Tabs {
        margin: 0 0 0 2.75rem;
        flex-shrink: 0; 

        .Tab {
          &.active {
            font-size: 1.25rem;
            font-weight: 600;
            color: #3F77F8;
            border-bottom: 3px solid #3F77F8;
            opacity: 1;
            white-space: nowrap; 
          }
        }
      }
    }

    .right-header-section {
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      align-items: center;
      flex-wrap: nowrap;
      gap: 0.75rem; 
      flex-shrink: 0;
      min-width: fit-content;

      .editor-controls {
        display: flex;
        gap: 0.75rem; 
        align-items: center;
        white-space: nowrap;

        svg.collapse-button {
          cursor: pointer;
          flex-shrink: 0; 
          width: 20px;
          height: 20px;

          &:hover path {
            fill: #3F77F8 !important;
            fill-opacity: 0.8;
          }

          &.collapsed {
            background: linear-gradient(to left, #000 29%, white 25%) !important;
          }
        }
      }
    }

    // Responsive styles
    @media (max-width: 1024px) {
      &.calculator-view {
        padding: 1.5rem 1rem; 
      }

      .left-header-section {
        .abun-logo {
          margin: 0 0.75rem; 
          width: 42px; 
          height: 38px;
        }

        .Tabs {
          margin: 0 0 0 1.5rem;

          .Tab.active {
            font-size: 1.125rem; 
          }
        }
      }

      .right-header-section {
        gap: 0.5rem; 

        .editor-controls {
          gap: 0.5rem;
        }
      }
    }

    @media (max-width: 768px) {
      &.calculator-view {
        height: auto;
        min-height: 60px;
        padding: 1rem 0.75rem;
      }

      .left-header-section {
        .back-btn {
          margin-right: 0.5rem;
          
          svg {
            width: 24px; 
            height: 20px;
          }
        }

        .abun-logo {
          margin: 0 0.5rem;
          width: 36px;
          height: 32px;
        }

        .Tabs {
          margin: 0 0 0 1rem;

          .Tab.active {
            font-size: 1rem;
          }
        }
      }

      .right-header-section {
        gap: 0.375rem; 

        .editor-controls {
          gap: 0.375rem;

          svg.collapse-button {
            width: 18px;
            height: 18px;
          }
        }
      }
    }

    @media (max-width: 480px) {
      &.calculator-view {
        padding: 0.75rem 0.5rem;
      }

      .left-header-section {
        .back-btn {
          margin-right: 0.25rem;
        }

        .abun-logo {
          margin: 0 0.25rem;
          width: 32px;
          height: 28px;
        }

        .Tabs {
          margin: 0 0 0 0.5rem;

          .Tab.active {
            font-size: 0.9rem;
          }
        }
      }

      .right-header-section {
        .editor-controls {
          svg.collapse-button {
            width: 16px;
            height: 16px;
          }
        }
      }
    }
  }

  // Content styles
  .calculator-page-generator-content {
    &.calculator-view {
      padding: 0;
      height: calc(100vh - 6rem);
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }
  }
}

// calculator Result Section
.calculator-result-section-new {
  display: flex;
  gap: 0;
  height: 100%;
  flex: 1;
  overflow: hidden;

  .calculator-preview-main {
    flex: 1;
    min-width: 0;
    background-color: #fff;
    border-radius: 0;
    border: none;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .ai-preview-container {
      padding: 16px;
      height: 100%;
      overflow: hidden;
      flex: 1;
      display: flex;
      flex-direction: column;

      .ai-preview-content {
        width: 100%;
        min-height: 100%;

        &:focus {
          outline: none !important;
          border: none !important;
        }

        &:focus-visible {
          outline: none !important;
          border: none !important;
        }

        &:focus {
          box-shadow: none !important;
        }
      }
    }
  }

  // Sidebar styles
  .calculator-sidebar {
    width: 380px;
    flex-shrink: 0;
    background-color: #fff;
    border-radius: 0;
    border: none;
    border-left: 1px solid #e5e7eb;
    height: 100%;
    overflow-y: auto;
    margin-top: 0;
    transition: all 0.3s ease;
    
    &.collapsed {
      width: 0;
      overflow: hidden;
      border-left: none;
    }

    .sidebar-section {
      border-bottom: 1px solid #e5e7eb;

      .sidebar-dropdown-header {
        padding: 16px 20px 0px 20px;
        cursor: pointer;
        background-color: #fff;
        font-weight: 400;
        font-size: 15px;
        color: #4b5563;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        transition: all 0.2s ease;
        border: none;
        margin-bottom: 16px; 

        h6 {
          font-size: 1.125rem;
          font-weight: 400;
          color: #3F77F8;
          width: 100%;
          font-family: Inter;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: flex-start;
          cursor: pointer;
        }

        &::before {
          content: "";
          width: 12px;
          height: 7px;
          background-image: url("data:image/svg+xml,%3Csvg width='12' height='7' viewBox='0 0 12 7' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 5.25L6.5 0L13 5.25L12 6.5L6.5 2.5L1 6.5L0 5.25Z' fill='%236B7280' /%3E%3C/svg%3E");
          background-repeat: no-repeat;
          background-position: center;
          background-size: contain;
          transition: transform 0.2s ease;
          flex-shrink: 0;
        }

        &.active {
          margin-bottom: 0; 
          
          &::before {
            transform: rotate(180deg);
          }
        }

        &:hover {
          background-color: #f9fafb;
        }

        &.version-header {
          justify-content: flex-start;
          gap: 12px;

          .version-count {
            background-color: #3b82f6;
            color: #fff;
            padding: 3px 10px;
            border-radius: 14px;
            font-size: 13px;
            min-width: 22px;
            text-align: center;
            font-weight: 500;
            margin-left: auto;
          }
        }

        span {
          font-size: 15px;
          color: #4b5563;
          font-weight: 400;
          line-height: 1.4;
        }
      }

      .sidebar-dropdown-content {
        display: none;
        padding: 20px 24px;
        background-color: #fff;

        .sidebar-textarea {
          width: 100%;
          min-height: 100px;
          padding: 14px 16px;
          border: 1px solid #d1d5db;
          border-radius: 8px;
          resize: vertical;
          margin-bottom: 20px;
          font-size: 14px;
          font-family: inherit;
          box-sizing: border-box;
          color: #374151;
          line-height: 1.5;

          &:focus {
            border-color: #3F77F8;
          }

          &.embed-code {
            background-color: #f9fafb;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            min-height: 80px;
            margin-bottom: 5px;
          }

          &::placeholder {
            color: #9ca3af;
          }
        }

        .sidebar-button {
          width: 100%;
          padding: 12px 18px;
          color: #fff;
          border: none;
          border-radius: 8px;
          cursor: pointer;
          font-size: 14px;
          font-weight: 500;
          transition: all 0.2s ease;

          &.update-btn {         
            background-color: #3b82f6;          

            &:hover:not(:disabled) {             
              background-color: #2563eb;         
            }          

            &:disabled {             
              opacity: 0.6;             
              cursor: not-allowed;         
            }     
          }      

          .button-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            width: 100%;
          }

          .spinner {         
            width: 12px;         
            height: 12px;         
            border: 2px solid #ffffff;         
            border-top: 2px solid transparent;         
            border-radius: 50%;         
            animation: spin 1s linear infinite;
            flex-shrink: 0;
          }      

          @keyframes spin {         
            0% { transform: rotate(0deg); }         
            100% { transform: rotate(360deg); }     
          } 

          &.copy-btn {
            background-color: #007bff;
            width: auto; 
            padding: 6px 12px;
            font-size: 12px; 
            margin-left: auto;
            margin-top: 0px; 
            margin-bottom: 0px; 
            display: block; 

            &:hover {
              background-color: #2a7ad0;
            }
          }

          &.small {
            width: auto;
            padding: 6px 10px;
            font-size: 9px;

            &.switch-btn {
              background-color: #3b82f6;

              &:hover {
                background-color: #2563eb;
              }
            }
          }
        }

        .versions-list-sidebar {
          max-height: 400px;
          overflow-y: auto;

          .version-item-sidebar {
            padding: 16px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin-bottom: 12px;
            background-color: #fff;
            transition: all 0.2s ease;

            &:hover {
              border-color: #d1d5db;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            }

            &.current-version {
              background-color: #eff6ff;
              border-color: #3b82f6;
            }

            .version-header-sidebar {
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              width: 100%;

              .version-info-sidebar {
                flex: 1;
                min-width: 0;
              }

              .version-actions-sidebar {
                display: flex;
                gap: 8px;
                flex-shrink: 0;
                margin-left: auto;
                align-items: flex-start;
                min-width: fit-content; 
                justify-content: flex-end; 
                width: auto;
              }
            }

            .version-number-sidebar {
              span {
                // Version number styles
              }
            }

            .version-description-sidebar {
              font-size: 13px;
              color: #6b7280;
              line-height: 1.5;
            }
          }

          .empty-versions {
            text-align: center;
            color: #6b7280;
            font-style: italic;
            font-size: 14px;
            margin: 0;
            padding: 24px;
          }
        }
      }

      &:has(.sidebar-dropdown-header.active) .sidebar-dropdown-content {
        display: block;
      }
    }
  }
}

// Loading Container
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 40px;
  flex: 1;
}

// Mobile responsive styles
@media (max-width: 768px) {
  .calculator-page-generator-content.calculator-view {
    height: calc(100vh - 5rem); 
  }
  
  .calculator-sidebar {
    &:not(.collapsed) {
      position: fixed;
      top: 60px; 
      left: 0;
      width: 100vw;
      height: calc(100vh - 60px);
      z-index: 1001;
      background-color: white;
    }
  }
}

@media (max-width: 480px) {
  .calculator-page-generator-content.calculator-view {
    height: calc(100vh - 4rem); 
  }
}

  /* Browser Mockup Styles */
  .browser-mockup {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    background-color: #ffffff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    flex: 1; // Take up all available space in the preview section
    margin: 0; // Remove any default margins
    padding: 0; // Remove any default padding

    .browser-header {
      display: flex;
      align-items: center;
      background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
      padding: 12px 16px;
      border-bottom: 1px solid #d0d0d0;
      min-height: 44px;

      .browser-controls {
        display: flex;
        gap: 8px;
        margin-right: 16px;

        .browser-control {
          width: 12px;
          height: 12px;
          border-radius: 50%;

          &.close {
            background-color: #ff5f57;
            border: 1px solid #e0443e;
          }

          &.minimize {
            background-color: #ffbd2e;
            border: 1px solid #dea123;
          }

          &.maximize {
            background-color: #28ca42;
            border: 1px solid #1aab29;
          }
        }
      }

      .browser-address-bar {
        flex: 1;
        background-color: #ffffff;
        border: 1px solid #d0d0d0;
        border-radius: 6px;
        padding: 6px 12px;
        font-family: $secondary-font;

        .browser-url {
          color: #666666;
          font-size: 14px;
          font-weight: 400;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          display: block;
        }
      }
    }

    .browser-notification-bar {
      background: #E6EBEE;
      border-bottom: 1px solid #d0d0d0;
      padding: 8px 16px;
      color: #000;
      font-size: 13px;

      .notification-content {
        display: flex;
        align-items: center;
        justify-content: center;

        .notification-text {
          font-weight: 500;
          color: #000;
        }
      }
    }

    .browser-content {
      background-color: #ffffff;
      flex: 1;
      display: flex;
      flex-direction: column;
      position: relative;

      .ai-preview-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        border-bottom: 1px solid #eee;
        background-color: #fafafa;

        h3 {
          font-family: $primary-font;
          font-size: 1.25rem;
          margin: 0;
          color: #333;
        }

        .ai-preview-actions {
          display: flex;
          gap: 8px;

          .ai-action-button {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            border: none;
            background-color: #f0f0f0;
            color: $grey-darker;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.875rem;
            font-family: $secondary-font;
            transition: background-color 0.2s;

            &:hover {
              background-color: #e0e0e0;
            }

            svg {
              width: 16px;
              height: 16px;
            }
          }
        }
      }

      .ai-preview-container {
        padding: 0;
        flex: 1;
        overflow: hidden; // Remove scroll, let content fill the space
        border: none;
        display: flex;
        flex-direction: column;
        height: 100%;

        .ai-preview-content {
          width: 100%;
          height: 100%;
          flex: 1;
          overflow: hidden; // Prevent internal scrolling
        }
      }
    }

    /* Loading overlay for browser mockup */
    .browser-loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.95);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;
      backdrop-filter: blur(2px);

      .browser-loading-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px;

        .browser-loading-spinner {
          width: 40px;
          height: 40px;
          border: 3px solid #f3f3f3;
          border-top: 3px solid #2e64fe;
          border-radius: 50%;
          animation: browser-spin 1s linear infinite;
        }

        .browser-loading-text {
          color: #666;
          font-size: 16px;
          font-weight: 500;
          font-family: $primary-font;
          margin: 0;
        }
      }
    }

    @keyframes browser-spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    @media (max-width: 768px) {
      .browser-header {
        padding: 8px 12px;
        min-height: 36px;

        .browser-controls {
          gap: 6px;
          margin-right: 12px;

          .browser-control {
            width: 10px;
            height: 10px;
          }
        }

        .browser-address-bar {
          padding: 4px 8px;

          .browser-url {
            font-size: 12px;
          }
        }
      }

      .browser-notification-bar {
        padding: 6px 12px;
        font-size: 12px;
      }
    }

    @media (max-width: 480px) {
      .browser-header {
        padding: 6px 8px;
        min-height: 32px;

        .browser-controls {
          gap: 4px;
          margin-right: 8px;

          .browser-control {
            width: 8px;
            height: 8px;
          }
        }

        .browser-address-bar {
          padding: 3px 6px;

          .browser-url {
            font-size: 11px;
          }
        }
      }

      .browser-notification-bar {
        padding: 4px 8px;
        font-size: 11px;
      }

      .browser-loading-overlay {
        .browser-loading-content {
          gap: 12px;

          .browser-loading-spinner {
            width: 30px;
            height: 30px;
            border-width: 2px;
          }

          .browser-loading-text {
            font-size: 14px;
          }
        }
      }
    }
  }
}