import {
    faArrow<PERSON>ef<PERSON>, fa<PERSON><PERSON>, fa<PERSON><PERSON>, fa<PERSON><PERSON><PERSON>, fa<PERSON><PERSON><PERSON><PERSON>, faRobot
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useQuery } from "@tanstack/react-query";
import { useRef, useEffect, useState } from "react";
import { Helmet } from 'react-helmet';
import { useNavigate, useParams } from "react-router-dom";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
import { getAIShareWidgetDetail, verifyToolsLoadingScriptMutation } from "../../utils/api";
import { useMutation } from "@tanstack/react-query";
import AbunButton from "../../components/AbunButton/AbunButton";
import './AIShareWidget.min.css';
import { ColumnDef, createColumnHelper } from "@tanstack/react-table";
import AbunTable from "../../components/AbunTable/AbunTable";

interface ClicksByLLM {
    [llm: string]: number;
}

interface URLClicksByLLM {
    [llm: string]: number;
}

interface URLStats {
    total: number;
    by_llm: URLClicksByLLM;
}

interface DestinationURLsStats {
    [url: string]: URLStats;
}

interface Widget {
    id: string;
    widget_id: string;
    name: string;
    prompt_template: string;
    text_before_button: string;
    selected_llms: string[];
    style: string;
    html_code: string;
    html_code_dark: string;
    is_processing: boolean;
    total_clicks: number;
    created_at: string;
    updated_at: string;
    total_clicks_by_llm: ClicksByLLM;
    destination_urls_stats: DestinationURLsStats;
    encrypted_id?: string;
    encrypted_tool_id?: string;
    ai_share_widget_script_verified?: boolean;
}

interface AnalyticsRowData {
    url: string;
    total: number;
    chatgpt: number;
    google: number;
    claude: number;
    grok: number;
    perplexity: number;
}

function AIShareWidgetView() {
    const { widgetId } = useParams<{ widgetId: string }>();
    const navigate = useNavigate();
    const [activeTab, setActiveTab] = useState("embed"); // Changed: Set default tab to "embed"
    const [isVerifying, setIsVerifying] = useState(false);
    const [isVerified, setIsVerified] = useState(false);
    const [widgetScriptTag, setWidgetScriptTag] = useState("");
    const [widgetDivTag, setWidgetDivTag] = useState("");

    const [analyticsData, setAnalyticsData] = useState<AnalyticsRowData[]>([]);
    const analyticsPageSizes = [10, 25, 50];


    // 5. Add these column definitions (add this with your other column definitions)
    const analyticsColumnHelper = createColumnHelper<AnalyticsRowData>();
    const analyticsColumnDefs: ColumnDef<AnalyticsRowData, any>[] = [
        analyticsColumnHelper.accessor('url', {
            id: 'url',
            header: "URL",
            cell: (props) => {
                const url = props.row.original.url;
                return (
                    <a
                        href={url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="has-text-link"
                        style={{ wordBreak: 'break-all' }}
                    >
                        {url.length > 50 ? `${url.substring(0, 50)}...` : url}
                    </a>
                );
            },
            enableGlobalFilter: true
        }),
        analyticsColumnHelper.accessor('total', {
            id: 'total',
            header: "Total Shares",
            cell: (props) => (
                <span className="has-text-weight-semibold">
                    {props.row.original.total}
                </span>
            ),
            meta: {
                align: 'center'
            }
        }),
        analyticsColumnHelper.accessor('chatgpt', {
            id: 'chatgpt',
            header: "ChatGPT",
            cell: (props) => props.row.original.chatgpt,
            meta: {
                align: 'center'
            }
        }),
        analyticsColumnHelper.accessor('google', {
            id: 'google',
            header: "Google AI",
            cell: (props) => props.row.original.google,
            meta: {
                align: 'center'
            }
        }),
        analyticsColumnHelper.accessor('claude', {
            id: 'claude',
            header: "Claude",
            cell: (props) => props.row.original.claude,
            meta: {
                align: 'center'
            }
        }),
        analyticsColumnHelper.accessor('grok', {
            id: 'grok',
            header: "Grok",
            cell: (props) => props.row.original.grok,
            meta: {
                align: 'center'
            }
        }),
        analyticsColumnHelper.accessor('perplexity', {
            id: 'perplexity',
            header: "Perplexity",
            cell: (props) => props.row.original.perplexity,
            meta: {
                align: 'center'
            }
        })
    ];

    // --------------------------- REFS ---------------------------
    const errorAlertRef = useRef<any>(null);
    const successAlertRef = useRef<any>(null);

    // Mutation for verifying script
    const verifyScriptMutation = useMutation(verifyToolsLoadingScriptMutation);


    const backToList = () => {
        navigate("/ai-share-widget");
    };

    // Fixed useQuery with proper undefined handling
    const { data: widgetData, isLoading, error, refetch } = useQuery({
        queryKey: ['ai-share-widget-detail', widgetId],
        queryFn: () => {
            if (!widgetId) {
                throw new Error('Widget ID is required');
            }
            return getAIShareWidgetDetail(widgetId);
        },
        enabled: !!widgetId,

        refetchIntervalInBackground: false, // Don't refetch when tab is not active
        staleTime: 30000, // Consider data stale after 30 seconds
        retry: 3, // Retry failed requests 3 times
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    });

    const widget: Widget | null = widgetData?.data?.widget || null;

    // Set script tags and verification status when widget data is available
    useEffect(() => {
        if (widget && widget.encrypted_id && widget.encrypted_tool_id) {
            setWidgetScriptTag(`<script async src="${process.env.REACT_APP_FRONTEND_DOMAIN}/js/${getSnippetFileName()}" data-user-id="${widget.encrypted_id}" data-tool-name="ai-share-widget"></script>`);
            setWidgetDivTag(`<div data-ai-share-widget-id="${widget.encrypted_tool_id}"></div>`);
            setIsVerified(widget.ai_share_widget_script_verified || false);
        }
    }, [widget]);

    // Force a manual refetch every 5 seconds if still processing
    useEffect(() => {
        let intervalId: NodeJS.Timeout;

        if (widget?.is_processing) {
            console.log('Setting up manual refetch interval for processing widget');
            intervalId = setInterval(() => {
                console.log('Manual refetch triggered');
                refetch();
            }, 5000);
        }

        return () => {
            if (intervalId) {
                console.log('Clearing manual refetch interval');
                clearInterval(intervalId);
            }
        };
    }, [widget?.is_processing, refetch]);



    useEffect(() => {
        if (widget?.destination_urls_stats && Object.keys(widget.destination_urls_stats).length > 0) {
            const processedData: AnalyticsRowData[] = Object.entries(widget.destination_urls_stats)
                .sort(([, a], [, b]) => b.total - a.total)
                .map(([url, stats]) => ({
                    url,
                    total: stats.total,
                    chatgpt: stats.by_llm?.['chatgpt'] || 0,
                    google: stats.by_llm?.['google-ai'] || stats.by_llm?.['google'] || 0,
                    claude: stats.by_llm?.['claude'] || 0,
                    grok: stats.by_llm?.['grok'] || 0,
                    perplexity: stats.by_llm?.['perplexity'] || 0
                }));
            setAnalyticsData(processedData);
        } else {
            setAnalyticsData([]);
        }
    }, [widget?.destination_urls_stats]);


    // Calculate total clicks across all LLMs
    const getTotalClicks = () => {
        if (!widget?.total_clicks_by_llm) return 0;
        return Object.values(widget.total_clicks_by_llm).reduce((sum, count) => sum + count, 0);
    };

    // Get most popular LLM
    const getMostPopularLLM = () => {
        if (!widget?.total_clicks_by_llm) return null;
        const entries = Object.entries(widget.total_clicks_by_llm);
        if (entries.length === 0) return null;
        return entries.reduce((max, current) => current[1] > max[1] ? current : max);
    };



    // Handle case where widgetId is undefined
    if (!widgetId) {
        return (
            <div className="ai-share-widget-container w-100">
                <div className="has-text-centered mt-6">
                    <p className="has-text-danger">Invalid widget ID.</p>
                    <button
                        className="button is-link mt-3"
                        onClick={() => navigate('/ai-share-widget')}
                    >
                        <FontAwesomeIcon icon={faArrowLeft} className="mr-2" />
                        Back to Widgets
                    </button>
                </div>
            </div>
        );
    }

    // Helper function to get snippet filename based on environment
    const getSnippetFileName = () => {
        if (process.env.REACT_APP_DRF_DOMAIN === 'https://api.abun.com') {
            return 'ai-share-widget-snippet.js';
        } else if (process.env.REACT_APP_DRF_DOMAIN === 'https://staging.api.abun.com') {
            return 'ai-share-widget-snippet-staging.js';
        } else {
            return 'ai-share-widget-snippet-dev.js';
        }
    };

    // Verification handler
    const handleVerifyScript = () => {
        setIsVerifying(true);

        verifyScriptMutation.mutate({ toolName: "ai-share-widget" }, {
            onSuccess: (response: any) => {
                setIsVerifying(false);
                const responseData = response.data;

                if (responseData.success) {
                    setIsVerified(true);
                    successAlertRef.current?.show("Script verification successful! Your website is properly configured.");
                    setTimeout(() => {
                        successAlertRef.current?.close();
                    }, 5000);
                } else {
                    // Handle different error types based on err_id
                    let errorMessage = responseData.message || "Verification failed";

                    switch (responseData.err_id) {
                        case "NO_WEBSITE_FOUND":
                            errorMessage = "No website found. Please ensure your website is properly connected.";
                            break;
                        case "WEBSITE_NOT_ACCESSIBLE":
                            errorMessage = "Website not accessible. Please check if your website is online and accessible.";
                            break;
                        case "SCRIPT_TAG_NOT_FOUND":
                            errorMessage = "Script tag not found on your website. Please ensure the script is properly installed.";
                            break;
                        case "INVALID_USER_EMAIL":
                            errorMessage = "Invalid user email. Please contact support if this issue persists.";
                            break;
                        default:
                            errorMessage = responseData.message || "Verification failed. Please try again.";
                    }

                    errorAlertRef.current?.show(errorMessage);
                    setTimeout(() => {
                        errorAlertRef.current?.close();
                    }, 5000);
                }
            },
            onError: (error: any) => {
                setIsVerifying(false);
                console.error('Error verifying script:', error);

                const errorMessage = error?.response?.data?.message || "Failed to verify script. Please try again.";
                errorAlertRef.current?.show(errorMessage);
                setTimeout(() => {
                    errorAlertRef.current?.close();
                }, 5000);
            }
        });
    };

    const copyToClipboard = (text: string) => {
        navigator.clipboard.writeText(text).then(() => {
            successAlertRef.current?.show("Copied to clipboard!");
        }).catch(err => {
            errorAlertRef.current?.show("Failed to copy to clipboard.");
        });
    };

    const formatDate = (dateString) => {
        const date = new Date(dateString);

        // Get day with ordinal suffix (1st, 2nd, 3rd, etc.)
        const day = date.getDate();
        const getOrdinalSuffix = (day) => {
            if (day > 3 && day < 21) return 'th';
            switch (day % 10) {
                case 1: return 'st';
                case 2: return 'nd';
                case 3: return 'rd';
                default: return 'th';
            }
        };

        // Get month name
        const months = [
            'January', 'February', 'March', 'April', 'May', 'June',
            'July', 'August', 'September', 'October', 'November', 'December'
        ];
        const month = months[date.getMonth()];

        // Get year
        const year = date.getFullYear();

        return `${day}${getOrdinalSuffix(day)} ${month} ${year}`;
    };



    if (isLoading && !widget) {
        return (
            <div className="ai-share-widget-container w-100">
                <div className="has-text-centered mt-6">
                    <FontAwesomeIcon icon={faSpinner} spin size="2x" />
                    <p className="mt-3">Loading widget details...</p>
                </div>
            </div>
        );
    }

    if (error && !widget) {
        return (
            <div className="ai-share-widget-container w-100">
                <div className="has-text-centered mt-6">
                    <p className="has-text-danger">Failed to load widget details.</p>
                    <button
                        className="button is-link mt-3"
                        onClick={() => navigate('/ai-share-widget')}
                    >
                        <FontAwesomeIcon icon={faArrowLeft} className="mr-2" />
                        Back to Widgets
                    </button>
                </div>
            </div>
        );
    }

    if (!widget) {
        return (
            <div className="ai-share-widget-container w-100">
                <div className="has-text-centered mt-6">
                    <p className="has-text-danger">Widget not found.</p>
                    <button
                        className="button is-link mt-3"
                        onClick={() => navigate('/ai-share-widget')}
                    >
                        <FontAwesomeIcon icon={faArrowLeft} className="mr-2" />
                        Back to Widgets
                    </button>
                </div>
            </div>
        );
    }

    const totalClicks = getTotalClicks();
    const mostPopularLLM = getMostPopularLLM();

    return (
        <>
            <Helmet>
                <title>AI Share Widget | Abun.com</title>
                <meta
                    name="description"
                    content="View and manage your AI-powered widget with analytics"
                />
            </Helmet>
            <div className="ai-share-widget-view">
                <div className={`card ai-share-widget-view-header w-100`}>
                    <div className="left-header-section">
                        <span
                            className="back-btn"
                            onClick={backToList}
                        >
                            <svg
                                className="back-btn"
                                width="30"
                                height="24"
                                viewBox="0 0 30 24"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path d="M26.0435 12.0003H2.82031M2.82031 12.0003L12.8382 1.98242M2.82031 12.0003L12.8382 22.0181" stroke="black" strokeOpacity="0.5" strokeWidth="3" />
                            </svg>
                        </span>

                        <div className="Tabs">
                            <div className="Tab active">
                                AI Share Widget: {widget.name} <span className="widget-date">Created on {formatDate(widget.created_at)}</span>
                            </div>
                        </div>
                    </div>
                </div>


                <div className="ai-share-widget-container w-100">
                    {/* Quick Stats Cards */}
                    {!widget.is_processing && widget.html_code && (
                        <div className="ai-share-stats-cards mt-2 mb-5">
                            {/* Total Clicks Card */}
                            <div className="ai-share-stat-card total-clicks">
                                <div className="stat-header">
                                    <div className="stat-icon">
                                        <FontAwesomeIcon icon={faBolt} />
                                    </div>
                                    <div className="stat-label">Total Shares</div>
                                </div>
                                <div className="stat-value">{totalClicks || 0}</div>
                            </div>

                            {/* All LLM Cards - Show all supported LLMs, even with 0 clicks */}
                            {['chatgpt', 'google', 'claude', 'grok', 'perplexity'].map((llm) => {
                                const count = llm === 'google'
                                    ? (widget.total_clicks_by_llm?.['google-ai'] || widget.total_clicks_by_llm?.['google'] || 0)
                                    : (widget.total_clicks_by_llm?.[llm] || 0);

                                const getLLMConfig = (llmName) => {
                                    switch (llmName.toLowerCase()) {
                                        case 'chatgpt':
                                            return {
                                                icon: 'https://staging.draftss2.opalstacked.com/wp-content/uploads/2025/07/chatgpt-2.png',
                                                className: 'chatgpt-card',
                                                displayName: 'Total ChatGPT Shares'
                                            };
                                        case 'google':
                                        case 'google-ai':
                                            return {
                                                icon: 'https://staging.draftss2.opalstacked.com/wp-content/uploads/2025/07/google.png',
                                                className: 'google-card',
                                                displayName: 'Total Google AI Shares'
                                            };
                                        case 'claude':
                                            return {
                                                icon: 'https://staging.draftss2.opalstacked.com/wp-content/uploads/2025/07/claude-2.png',
                                                className: 'claude-card',
                                                displayName: 'Total Claude Shares'
                                            };
                                        case 'grok':
                                            return {
                                                icon: 'https://staging.draftss2.opalstacked.com/wp-content/uploads/2025/07/grok-2.png',
                                                className: 'grok-card',
                                                displayName: 'Total Grok Shares'
                                            };
                                        case 'perplexity':
                                            return {
                                                icon: 'https://staging.draftss2.opalstacked.com/wp-content/uploads/2025/07/perplexity-2.png',
                                                className: 'perplexity-card',
                                                displayName: 'Total Perplexity Shares'
                                            };
                                        default:
                                            return {
                                                icon: null,
                                                className: 'default-card',
                                                displayName: `Total ${llm} Shares`
                                            };
                                    }
                                };

                                const config = getLLMConfig(llm);

                                return (
                                    <div key={llm} className={`ai-share-stat-card ${config.className}`}>
                                        <div className="stat-header">
                                            <div className="stat-icon">
                                                {config.icon ? (
                                                    <img src={config.icon} alt={config.displayName} />
                                                ) : (
                                                    <FontAwesomeIcon icon={faRobot} />
                                                )}
                                            </div>
                                            <div className="stat-label">{config.displayName}</div>
                                        </div>
                                        <div className="stat-value">{count}</div>
                                    </div>
                                );
                            })}
                        </div>

                    )}

                    <div className={`widget-view-layout ${activeTab === 'analytics' ? 'analytics-layout' : 'embed-layout'}`}>
                        {/* Left Section - Tabs */}
                        <div className="widget-left-section">
                            <div className="tabs-container">
                                <div className="tabs is-medium">
                                    <ul>
                                        <li className={activeTab === "analytics" ? "is-active" : ""}>
                                            <a onClick={() => setActiveTab("analytics")}>
                                                <FontAwesomeIcon icon={faChartBar} className="mr-2" />
                                                Analytics
                                            </a>
                                        </li>
                                        <li className={activeTab === "embed" ? "is-active" : ""}>
                                            <a onClick={() => setActiveTab("embed")}>
                                                <FontAwesomeIcon icon={faCopy} className="mr-2" />
                                                Embed Code
                                            </a>
                                        </li>
                                    </ul>
                                </div>

                                <div className="tab-content">
                                    {activeTab === "embed" && (
                                        <div className="tab-pane embed-tab">
                                            {/* Integration Instructions */}
                                            <div className="integration-instructions">
                                                <p><strong>Integration instructions:</strong></p>
                                                <ul>
                                                    <li>Choose what widget theme do you want to display, light or dark.</li>
                                                    <li>Copy the entire script and add it wherever you want to load the widget.</li>
                                                </ul>
                                            </div>

                                            <div className="embed-layout-container">
                                                {/* Light Mode Section */}
                                                <div className="light-mode-section">
                                                    {/* Light Mode Preview */}
                                                    <div className="light-preview-container">
                                                        <div className="preview-widget" style={{ padding: '1rem 0', margin: '0 -1rem' }}>
                                                            {widget.html_code && !widget.is_processing ? (
                                                                <iframe
                                                                    srcDoc={widget.html_code}
                                                                    style={{
                                                                        width: '100%',
                                                                        height: '100%',
                                                                        border: 'none',
                                                                        backgroundColor: 'white'
                                                                    }}
                                                                    sandbox="allow-scripts allow-same-origin allow-popups allow-popups-to-escape-sandbox"
                                                                    title="Light Mode Widget Preview"
                                                                />
                                                            ) : widget.is_processing ? (
                                                                <div className="preview-loading">
                                                                    <FontAwesomeIcon icon={faSpinner} spin size="2x" />
                                                                    <p>Generating widget preview...</p>
                                                                </div>
                                                            ) : (
                                                                <div className="preview-placeholder">
                                                                    <p>No preview available</p>
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>
                                                    {/* Light Mode Script */}
                                                    <div className="light-script-container">
                                                        <h3 className="script-title">Script for Light Mode</h3>
                                                        <div className="script-code-wrapper">
                                                            <textarea
                                                                className="script-textarea"
                                                                readOnly
                                                                value={`${widgetScriptTag}\n\n${widgetDivTag}`}
                                                            />
                                                            <button
                                                                className="copy-script-button"
                                                                onClick={() => copyToClipboard(`${widgetScriptTag}\n${widgetDivTag}`)}
                                                            >
                                                                Copy code
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>

                                                {/* Dark Mode Section */}
                                                <div className="dark-mode-section">
                                                    {/* Dark Mode Preview */}
                                                    <div className="dark-preview-container">
                                                        <div className="preview-widget" style={{ padding: '1rem 0', margin: '0 -1.5rem' }}>
                                                            {widget.html_code_dark && !widget.is_processing ? (
                                                                <iframe
                                                                    srcDoc={widget.html_code_dark}
                                                                    style={{
                                                                        width: '100%',
                                                                        height: '100%',
                                                                        border: 'none',
                                                                        backgroundColor: 'white'
                                                                    }}
                                                                    sandbox="allow-scripts allow-same-origin allow-popups allow-popups-to-escape-sandbox"
                                                                    title="Dark Mode Widget Preview"
                                                                />
                                                            ) : widget.is_processing ? (
                                                                <div className="preview-loading">
                                                                    <FontAwesomeIcon icon={faSpinner} spin size="2x" />
                                                                    <p>Generating widget preview...</p>
                                                                </div>
                                                            ) : (
                                                                <div className="preview-placeholder">
                                                                    <p>No preview available</p>
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>

                                                    {/* Dark Mode Script */}
                                                    <div className="dark-script-container">
                                                        <h3 className="script-title">Script for Dark Mode</h3>
                                                        <div className="script-code-wrapper">
                                                            <textarea
                                                                className="script-textarea"
                                                                readOnly
                                                                value={`${widgetScriptTag}\n\n${widgetDivTag}`}
                                                            />
                                                            <button
                                                                className="copy-script-button"
                                                                onClick={() => copyToClipboard(`${widgetScriptTag}\n${widgetDivTag}`)}
                                                            >
                                                                Copy code
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            {/* Verification Section */}
                                            {widget && !widget.is_processing && (
                                                <div style={{ marginTop: '30px', padding: '20px', border: '1px solid #e0e0e0', borderRadius: '8px' }}>
                                                    <h3 className="script-title">Verify Script Installation</h3>
                                                    <p style={{ marginBottom: '15px', color: '#666' }}>
                                                        Verify that the script is properly installed on your website:
                                                    </p>
                                                    <AbunButton
                                                        style={{
                                                            background: isVerified ? '#10B981' : '#007bff',
                                                            borderColor: isVerified ? '#10B981' : '#007bff',
                                                            width: '200px'
                                                        }}
                                                        type="success"
                                                        clickHandler={handleVerifyScript}
                                                        disabled={isVerifying || isVerified}
                                                    >
                                                        {isVerified ? 'Verified' : isVerifying ? 'Verifying...' : 'Verify'}
                                                    </AbunButton>
                                                </div>
                                            )}
                                        </div>
                                    )}

                                    {activeTab === "analytics" && (
                                        <div className="tab-pane analytics-tab">
                                            <div className="analytics-content">
                                                <div className="analytics-stats-row">
                                                    <div className="chart-container" style={{ padding: '1rem 0', margin: '0 -1rem' }}>
                                                        <div style={{ width: '100%' }}>
                                                            <AbunTable
                                                                tableContentName={"URL"}
                                                                tableData={analyticsData}
                                                                columnDefs={analyticsColumnDefs}
                                                                pageSizes={analyticsPageSizes}
                                                                initialPageSize={analyticsPageSizes[0]}
                                                                noDataText={"No data yet"}
                                                                searchboxPlaceholderText={"Search URLs..."}
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    )}

                                </div>
                            </div>
                        </div>

                        {/* Right Section - Preview (Only shown for Embed Code tab) */}
                        {activeTab === "embed" && (
                            <div className="widget-right-section">
                                <div className="preview-container">
                                    <div className="preview-content">
                                        {/* Light Mode Preview */}
                                        <div className="preview-mode">

                                            {widget.html_code && !widget.is_processing ? (
                                                <iframe
                                                    srcDoc={widget.html_code}
                                                    style={{
                                                        width: '100%',
                                                        height: '100%',
                                                        border: 'none',
                                                        backgroundColor: 'white',
                                                        display: 'block',
                                                        flex: 1
                                                    }}
                                                    sandbox="allow-scripts allow-same-origin allow-popups allow-popups-to-escape-sandbox"
                                                    title="Widget Preview"
                                                />
                                            ) : widget.is_processing ? (
                                                <div className="has-text-centered">
                                                    <FontAwesomeIcon icon={faSpinner} spin size="2x" />
                                                    <p className="mt-3">Generating widget preview...</p>
                                                </div>
                                            ) : (
                                                <div className="has-text-centered">
                                                    <p className="has-text-grey">No preview available</p>
                                                </div>
                                            )}

                                        </div>
                                        <br />
                                        {/* Dark Mode Preview */}
                                        <div className="preview-mode">

                                            {widget.html_code_dark && !widget.is_processing ? (
                                                <iframe
                                                    srcDoc={widget.html_code_dark}
                                                    style={{
                                                        width: '100%',
                                                        height: '100%',
                                                        border: 'none',
                                                        backgroundColor: 'white',
                                                        display: 'block',
                                                        flex: 1
                                                    }}
                                                    sandbox="allow-scripts allow-same-origin allow-popups allow-popups-to-escape-sandbox"
                                                    title="Widget Preview"
                                                />
                                            ) : widget.is_processing ? (
                                                <div className="has-text-centered">
                                                    <FontAwesomeIcon icon={faSpinner} spin size="2x" />
                                                    <p className="mt-3">Generating widget preview...</p>
                                                </div>
                                            ) : (
                                                <div className="has-text-centered">
                                                    <p className="has-text-grey">No preview available</p>
                                                </div>
                                            )}

                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            <ErrorAlert ref={errorAlertRef} />
            <SuccessAlert ref={successAlertRef} />
        </>
    );

}

export default AIShareWidgetView;